<template>
  <Tooltip placement="top">
    <template #title>
      <span>{{ t('common.redo') }}</span>
    </template>
    <Button class="!inline-flex !items-center !px-2"><RedoOutlined @click="redo" /></Button>
    
  </Tooltip>
</template>
<script lang="ts" setup>
  import { Tooltip, Button } from 'ant-design-vue';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useTableContext } from '../../hooks/useTableContext';

  defineOptions({ name: 'RedoSetting' });

  const table = useTableContext();
  const { t } = useI18n();

  function redo() {
    table.reload();
  }
</script>
